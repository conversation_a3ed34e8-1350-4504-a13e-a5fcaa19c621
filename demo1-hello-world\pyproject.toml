[project]
name = "fastapi-demo1-hello-world"
version = "1.0.0"
description = "FastAPI Demo 1: Hello World API - 基础入门项目"
authors = [
    {name = "FastAPI学习者", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "httpx>=0.25.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "httpx>=0.25.0",
]
