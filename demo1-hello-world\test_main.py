"""
FastAPI Demo 1 测试文件
用于验证API功能是否正常工作
"""

from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_read_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "timestamp" in data

def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_create_item():
    """测试创建商品"""
    item_data = {
        "name": "测试商品",
        "price": 99.99,
        "description": "这是一个测试商品",
        "category": "测试分类",
        "in_stock": True
    }
    response = client.post("/items/", json=item_data)
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == item_data["name"]
    assert data["price"] == item_data["price"]
    assert "id" in data
    assert "created_at" in data

def test_read_item():
    """测试获取商品"""
    # 先创建一个商品
    item_data = {
        "name": "测试商品2",
        "price": 199.99,
        "category": "电子产品",
        "in_stock": True
    }
    create_response = client.post("/items/", json=item_data)
    item_id = create_response.json()["id"]
    
    # 获取商品
    response = client.get(f"/items/{item_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == item_id
    assert data["name"] == item_data["name"]

def test_read_items_with_filters():
    """测试商品列表过滤"""
    # 创建测试数据
    test_items = [
        {"name": "手机", "price": 999.99, "category": "电子产品", "in_stock": True},
        {"name": "书籍", "price": 29.99, "category": "图书", "in_stock": False},
        {"name": "耳机", "price": 199.99, "category": "电子产品", "in_stock": True}
    ]
    
    for item in test_items:
        client.post("/items/", json=item)
    
    # 测试分类过滤
    response = client.get("/items/?category=电子产品")
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 2  # 至少有手机和耳机
    
    # 测试价格过滤
    response = client.get("/items/?min_price=100&max_price=500")
    assert response.status_code == 200
    data = response.json()
    for item in data:
        assert 100 <= item["price"] <= 500

def test_update_item():
    """测试更新商品"""
    # 先创建商品
    item_data = {
        "name": "原始商品",
        "price": 100.0,
        "category": "测试",
        "in_stock": True
    }
    create_response = client.post("/items/", json=item_data)
    item_id = create_response.json()["id"]
    
    # 更新商品
    updated_data = {
        "name": "更新后的商品",
        "price": 150.0,
        "category": "新分类",
        "in_stock": False
    }
    response = client.put(f"/items/{item_id}", json=updated_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == updated_data["name"]
    assert data["price"] == updated_data["price"]

def test_delete_item():
    """测试删除商品"""
    # 先创建商品
    item_data = {
        "name": "待删除商品",
        "price": 50.0,
        "category": "测试",
        "in_stock": True
    }
    create_response = client.post("/items/", json=item_data)
    item_id = create_response.json()["id"]
    
    # 删除商品
    response = client.delete(f"/items/{item_id}")
    assert response.status_code == 200
    
    # 验证商品已删除
    get_response = client.get(f"/items/{item_id}")
    assert get_response.status_code == 404

def test_get_stats():
    """测试统计信息"""
    response = client.get("/stats")
    assert response.status_code == 200
    data = response.json()
    assert "total_items" in data
    assert "price_stats" in data
    assert "categories" in data

def test_item_validation():
    """测试数据验证"""
    # 测试无效价格
    invalid_item = {
        "name": "无效商品",
        "price": -10.0,  # 负价格应该被拒绝
        "category": "测试"
    }
    response = client.post("/items/", json=invalid_item)
    assert response.status_code == 422  # 验证错误
    
    # 测试空名称
    invalid_item2 = {
        "name": "",  # 空名称应该被拒绝
        "price": 100.0,
        "category": "测试"
    }
    response = client.post("/items/", json=invalid_item2)
    assert response.status_code == 422

if __name__ == "__main__":
    import pytest
    pytest.main([__file__])
