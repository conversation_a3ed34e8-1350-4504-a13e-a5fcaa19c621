"""
FastAPI Demo 1: Hello World API
基础入门项目 - 学习FastAPI核心概念

这个demo包含：
- 基本GET和POST路由
- 路径参数和查询参数处理
- Pydantic数据模型
- 自动API文档生成
"""

from fastapi import FastAPI, HTTPException, Query
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# 创建FastAPI应用实例
app = FastAPI(
    title="Hello World API",
    description="FastAPI Demo 1 - 学习基础概念的入门项目",
    version="1.0.0",
    docs_url="/docs",  # Swagger UI文档地址
    redoc_url="/redoc"  # ReDoc文档地址
)

# Pydantic数据模型定义
class Item(BaseModel):
    """商品模型"""
    name: str = Field(..., min_length=1, max_length=100, description="商品名称")
    price: float = Field(..., gt=0, description="商品价格，必须大于0")
    description: Optional[str] = Field(None, max_length=500, description="商品描述")
    category: str = Field(..., description="商品分类")
    in_stock: bool = Field(default=True, description="是否有库存")

class ItemResponse(BaseModel):
    """商品响应模型"""
    id: int
    name: str
    price: float
    description: Optional[str]
    category: str
    in_stock: bool
    created_at: datetime

class Message(BaseModel):
    """通用消息响应模型"""
    message: str
    timestamp: datetime

# 模拟数据存储（内存中）
items_db: List[ItemResponse] = []
item_id_counter = 1

# 路由定义

@app.get("/", response_model=Message)
def read_root():
    """
    根路径 - 返回欢迎消息
    这是最简单的GET路由示例
    """
    return Message(
        message="欢迎使用FastAPI Hello World API！",
        timestamp=datetime.now()
    )

@app.get("/health")
def health_check():
    """
    健康检查端点
    用于验证API服务是否正常运行
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "1.0.0"
    }

@app.get("/items/{item_id}", response_model=ItemResponse)
def read_item(item_id: int):
    """
    根据ID获取商品信息
    演示路径参数的使用
    
    Args:
        item_id: 商品ID（路径参数）
    """
    # 查找商品
    item = next((item for item in items_db if item.id == item_id), None)
    if not item:
        raise HTTPException(status_code=404, detail=f"商品ID {item_id} 不存在")
    return item

@app.get("/items/", response_model=List[ItemResponse])
def read_items(
    category: Optional[str] = Query(None, description="按分类过滤商品"),
    min_price: Optional[float] = Query(None, ge=0, description="最低价格"),
    max_price: Optional[float] = Query(None, ge=0, description="最高价格"),
    in_stock: Optional[bool] = Query(None, description="是否有库存"),
    limit: int = Query(10, ge=1, le=100, description="返回商品数量限制")
):
    """
    获取商品列表
    演示查询参数的使用和数据过滤
    
    Args:
        category: 商品分类过滤
        min_price: 最低价格过滤
        max_price: 最高价格过滤
        in_stock: 库存状态过滤
        limit: 返回数量限制
    """
    filtered_items = items_db.copy()
    
    # 按分类过滤
    if category:
        filtered_items = [item for item in filtered_items if item.category.lower() == category.lower()]
    
    # 按价格范围过滤
    if min_price is not None:
        filtered_items = [item for item in filtered_items if item.price >= min_price]
    
    if max_price is not None:
        filtered_items = [item for item in filtered_items if item.price <= max_price]
    
    # 按库存状态过滤
    if in_stock is not None:
        filtered_items = [item for item in filtered_items if item.in_stock == in_stock]
    
    # 限制返回数量
    return filtered_items[:limit]

@app.post("/items/", response_model=ItemResponse, status_code=201)
def create_item(item: Item):
    """
    创建新商品
    演示POST请求和Pydantic模型验证
    
    Args:
        item: 商品信息（请求体）
    """
    global item_id_counter
    
    # 检查商品名称是否已存在
    existing_item = next((existing for existing in items_db if existing.name.lower() == item.name.lower()), None)
    if existing_item:
        raise HTTPException(status_code=400, detail=f"商品名称 '{item.name}' 已存在")
    
    # 创建新商品
    new_item = ItemResponse(
        id=item_id_counter,
        name=item.name,
        price=item.price,
        description=item.description,
        category=item.category,
        in_stock=item.in_stock,
        created_at=datetime.now()
    )
    
    items_db.append(new_item)
    item_id_counter += 1
    
    return new_item

@app.put("/items/{item_id}", response_model=ItemResponse)
def update_item(item_id: int, item: Item):
    """
    更新商品信息
    演示PUT请求和数据更新
    
    Args:
        item_id: 商品ID（路径参数）
        item: 更新的商品信息（请求体）
    """
    # 查找要更新的商品
    existing_item = next((existing for existing in items_db if existing.id == item_id), None)
    if not existing_item:
        raise HTTPException(status_code=404, detail=f"商品ID {item_id} 不存在")
    
    # 更新商品信息
    existing_item.name = item.name
    existing_item.price = item.price
    existing_item.description = item.description
    existing_item.category = item.category
    existing_item.in_stock = item.in_stock
    
    return existing_item

@app.delete("/items/{item_id}")
def delete_item(item_id: int):
    """
    删除商品
    演示DELETE请求
    
    Args:
        item_id: 商品ID（路径参数）
    """
    global items_db
    
    # 查找要删除的商品
    item_to_delete = next((item for item in items_db if item.id == item_id), None)
    if not item_to_delete:
        raise HTTPException(status_code=404, detail=f"商品ID {item_id} 不存在")
    
    # 删除商品
    items_db = [item for item in items_db if item.id != item_id]
    
    return Message(
        message=f"商品 '{item_to_delete.name}' 已成功删除",
        timestamp=datetime.now()
    )

@app.get("/stats")
def get_stats():
    """
    获取API统计信息
    演示数据聚合和计算
    """
    total_items = len(items_db)
    in_stock_items = len([item for item in items_db if item.in_stock])
    categories = list(set(item.category for item in items_db))
    
    if total_items > 0:
        avg_price = sum(item.price for item in items_db) / total_items
        max_price = max(item.price for item in items_db)
        min_price = min(item.price for item in items_db)
    else:
        avg_price = max_price = min_price = 0
    
    return {
        "total_items": total_items,
        "in_stock_items": in_stock_items,
        "out_of_stock_items": total_items - in_stock_items,
        "categories": categories,
        "price_stats": {
            "average": round(avg_price, 2),
            "maximum": max_price,
            "minimum": min_price
        },
        "timestamp": datetime.now()
    }

# 启动信息
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
