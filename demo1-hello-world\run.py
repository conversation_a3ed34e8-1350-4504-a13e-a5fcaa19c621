"""
FastAPI Demo 1 启动脚本
提供多种启动方式的便捷脚本
"""

import uvicorn
import os
from pathlib import Path

def main():
    """启动FastAPI应用"""
    
    # 获取环境变量配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "true").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "info")
    
    print("🚀 启动FastAPI Demo 1: Hello World API")
    print(f"📍 服务地址: http://{host}:{port}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"📖 ReDoc文档: http://{host}:{port}/redoc")
    print("🛑 按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level=log_level,
        access_log=True
    )

if __name__ == "__main__":
    main()
